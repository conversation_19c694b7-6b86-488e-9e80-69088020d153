/**
 * Cloudflare 验证 API
 * 提供简单的函数式调用接口
 */

import { createApp, h, ref } from "vue";
import { CF_TURNSTILE_TYPE, type TurnstileResult } from "./CloudflareMgr";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

// 验证选项接口
export interface VerifyOptions {
  /** 验证类型 */
  cfType: CF_TURNSTILE_TYPE;
  /** 弹窗标题 */
  title?: string;
  /** 描述文本 */
  description?: string;
  /** 是否显示取消按钮 */
  showCancelButton?: boolean;
  /** 自定义 Site Key */
  siteKey?: string;
  /** 主题 */
  theme?: "light" | "dark" | "auto";
  /** 尺寸 */
  size?: "normal" | "compact" | "invisible";
  /** 外观 */
  appearance?: "always" | "execute" | "interaction-only";
  /** 验证成功后自动关闭延迟时间(ms) */
  autoCloseDelay?: number;
}

// 验证结果接口
export interface VerifyResult {
  success: boolean;
  "cf-token"?: string;
  "cf-scene": string;
  error?: string;
  cancelled?: boolean;
  // 保持向后兼容的旧属性名
  token?: string;
  cfType?: string;
}

/**
 * 显示 Cloudflare 验证弹窗
 * @param options 验证选项
 * @returns Promise<VerifyResult>
 */
export function showCloudflareVerify(options: VerifyOptions): Promise<VerifyResult> {
  return new Promise((resolve) => {
    // 创建容器元素
    const container = document.createElement("div");
    document.body.appendChild(container);

    // 创建响应式状态
    const visible = ref(true);

    // 清理函数
    const cleanup = () => {
      setTimeout(() => {
        if (container && container.parentNode) {
          app.unmount();
          container.parentNode.removeChild(container);
        }
      }, 300); // 延迟清理，等待动画完成
    };

    // 处理验证成功
    const handleSuccess = (result: TurnstileResult) => {
      console.log("✅ Verification successful:", result);
      visible.value = false;
      cleanup();
      resolve({
        success: true,
        "cf-token": result["cf-token"],
        "cf-scene": result["cf-scene"],
        // 保持向后兼容
        token: result["cf-token"],
        cfType: result["cf-scene"],
      });
    };

    // 处理验证失败
    const handleError = (error: string) => {
      console.error("❌ Verification failed:", error);
      visible.value = false;
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        error,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 处理取消
    const handleCancel = () => {
      console.log("❌ Verification cancelled");
      visible.value = false;
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        cancelled: true,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 处理弹窗关闭
    const handleUpdateModelValue = (value: boolean) => {
      visible.value = value;
      if (!value) {
        cleanup();
        resolve({
          success: false,
          "cf-scene": options.cfType,
          cancelled: true,
          // 保持向后兼容
          cfType: options.cfType,
        });
      }
    };

    // 创建 Vue 应用实例
    const app = createApp({
      render() {
        return h(CloudflareVerifyDialog, {
          modelValue: visible.value,
          "onUpdate:modelValue": handleUpdateModelValue,
          cfType: options.cfType,
          title: options.title,
          description: options.description,
          showCancelButton: options.showCancelButton,
          siteKey: options.siteKey,
          theme: options.theme,
          size: options.size,
          appearance: options.appearance,
          autoCloseDelay: options.autoCloseDelay,
          onSuccess: handleSuccess,
          onError: handleError,
          onCancel: handleCancel,
        });
      },
    });

    // 挂载应用
    app.mount(container);
  });
}

/**
 * 快捷验证方法 - 登录验证
 */
export function verifyLogin(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
    title: "Login Verification",
    description: "Please complete the security verification to login.",
  });
}

/**
 * 快捷验证方法 - 注册验证
 */
export function verifyRegister(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.REGISTER_SUBMIT,
    title: "Registration Verification",
    description: "Please complete the security verification to register.",
  });
}

/**
 * 快捷验证方法 - KYC验证
 */
export function verifyKYC(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.KYC_SUBMIT,
    title: "KYC Verification",
    description: "Please complete the security verification for KYC submission.",
  });
}

/**
 * 快捷验证方法 - 提款验证
 */
export function verifyWithdrawal(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT,
    title: "Withdrawal Verification",
    description: "Please complete the security verification for withdrawal.",
  });
}

/**
 * 快捷验证方法 - 忘记密码验证
 */
export function verifyForgetPassword(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.FORGET_PW_SUBMIT,
    title: "Password Reset Verification",
    description: "Please complete the security verification to reset your password.",
  });
}

/**
 * 快捷验证方法 - 修改密码验证
 */
export function verifyChangePassword(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.MODIFY_LOGIN_PW_SUBMIT,
    title: "Change Password Verification",
    description: "Please complete the security verification to change your password.",
  });
}

/**
 * 快捷验证方法 - 绑定手机号验证
 */
export function verifyBindPhone(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.BIND_PHONE_SUBMIT,
    title: "Bind Phone Verification",
    description: "Please complete the security verification to bind your phone number.",
  });
}

/**
 * 快捷验证方法 - 修改手机号验证
 */
export function verifyChangePhone(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.MODIFY_PHONE_SUBMIT,
    title: "Change Phone Verification",
    description: "Please complete the security verification to change your phone number.",
  });
}

/**
 * 执行无感验证
 * 使用 Cloudflare Turnstile Invisible 模式进行无感验证
 * 访客不会看到任何小部件或指示，验证在后台静默进行
 * @param options 验证选项
 * @returns Promise<VerifyResult>
 */
export function executeSeamlessVerification(options: VerifyOptions): Promise<VerifyResult> {
  return new Promise((resolve) => {
    console.log("🔐 Starting executeSeamlessVerification with options:", options);

    // 创建隐藏的容器元素用于 Invisible 模式
    const container = document.createElement("div");
    container.style.position = "fixed";
    container.style.left = "-1000px";
    container.style.top = "-1000px";
    container.style.width = "300px";
    container.style.height = "200px";
    container.style.opacity = "0";
    container.style.pointerEvents = "none";
    container.style.zIndex = "-1";
    document.body.appendChild(container);

    // 无感验证需要设置为 true 才能初始化
    const visible = ref(true);

    // 清理函数
    const cleanup = () => {
      setTimeout(() => {
        if (container && container.parentNode) {
          app.unmount();
          container.parentNode.removeChild(container);
        }
      }, 100);
    };

    // 处理验证成功
    const handleSuccess = (result: TurnstileResult) => {
      console.log("✅ Invisible verification successful:", result);
      cleanup();
      resolve({
        success: true,
        "cf-token": result["cf-token"],
        "cf-scene": result["cf-scene"],
        // 保持向后兼容
        token: result["cf-token"],
        cfType: result["cf-scene"],
      });
    };

    // 处理验证失败
    const handleError = (error: string) => {
      console.error("❌ Invisible verification failed:", error);
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        error,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 处理取消（Invisible 模式通常不会有取消操作）
    const handleCancel = () => {
      console.log("❌ Invisible verification cancelled");
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        cancelled: true,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 无感验证的配置选项
    const seamlessOptions = {
      ...options,
      // 使用 interaction-only 模式实现无感验证
      appearance: "interaction-only" as const,
      size: "compact" as const, // 使用 compact 尺寸减少视觉影响
      theme: options.theme || "light",
      // 无感验证不显示任何 UI
      title: "Seamless Verification",
      description: "Processing...",
      showCancelButton: false,
      autoCloseDelay: 0,
    };

    console.log("🔧 Seamless options:", seamlessOptions);

    // 创建 Vue 应用实例用于 Invisible 验证
    const app = createApp({
      render() {
        return h(CloudflareVerifyDialog, {
          modelValue: visible.value,
          "onUpdate:modelValue": (value: boolean) => {
            console.log("📱 ModelValue changed:", value);
            visible.value = value;
          },
          cfType: seamlessOptions.cfType,
          siteKey: seamlessOptions.siteKey,
          theme: seamlessOptions.theme,
          size: seamlessOptions.size,
          appearance: seamlessOptions.appearance,
          title: seamlessOptions.title,
          description: seamlessOptions.description,
          showCancelButton: seamlessOptions.showCancelButton,
          autoCloseDelay: seamlessOptions.autoCloseDelay,
          onSuccess: handleSuccess,
          onError: handleError,
          onCancel: handleCancel,
        });
      },
    });

    // 挂载到隐藏容器，启动 Invisible 验证
    console.log("🚀 Mounting app to container");
    app.mount(container);

    // 添加超时处理，防止无限等待
    setTimeout(() => {
      if (!container.parentNode) return; // 已经清理过了

      console.log("⏰ Seamless verification timeout");
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        error: "Verification timeout",
        cfType: options.cfType,
      });
    }, 30000); // 30秒超时
  });
}

// 导出常用的验证类型，方便使用
export { CF_TURNSTILE_TYPE } from "./CloudflareMgr";
