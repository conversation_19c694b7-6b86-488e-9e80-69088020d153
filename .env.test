

# 接口前缀
VITE_API_URL=https://test.nustaronline.vip/
#前端域名地址
# VITE_WEB_URL = https://test-h5.nustaronline.vip
VITE_WEB_URL =http://localhost:7456


# 静态资源前缀
VITE_ASSETS_URL=https://uat-nustar-static.nustaronline.vip/

# Gcash 小程序商城链接
VITE_GCASH_SHOP_URL=https://gcashdev.page.link/?link=https://gcash.splashscreen/?redirect=gcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android.uat%2526isi%253D1358216762%2526ibi%253Dxyz.mynt.gcashdev&apn=com.globe.gcash.android.uat&ibi=xyz.mynt.gcashdev

# 跨域代理，可以配置多个，请注意不要换行
VITE_PROXY=[["/mock-api","http://localhost:8001"]]
# VITE_PROXY = [["/appApi","http://localhost:8001"],["/upload","http://localhost:8001/upload"]]

# 验证类型配置 (geetest | cloudflare)
VITE_VERIFICATION_TYPE=cloudflare

# Cloudflare Turnstile Site Keys
VITE_CF_SITE_KEY_GET_CODE=0x4AAAAAABr6n02z8VbwKkph
VITE_CF_SITE_KEY_GET_LOGIN=0x4AAAAAABr6liO_iAPr4Zx_

# Cloudflare Turnstile 外链配置  cocos项目单独部署使用的校验页面
VITE_CF_TURNSTILE_HOST=https://wayfocus.nustaronline.vip/

# 是否删除console
VITE_DROP_CONSOLE=false

# 是否启用 VConsole 调试工具
VITE_ENABLE_VCONSOLE=true
